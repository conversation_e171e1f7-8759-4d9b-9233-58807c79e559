"""
SAMAY API Routes
REST API endpoints for the railway traffic management system
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from ..models.railway_models import (
    Train, Disruption, Recommendation, SectionState, 
    SimulationScenario, PerformanceMetrics
)
from ..core.config import settings

logger = logging.getLogger(__name__)

# Create API router
api_router = APIRouter()

# Global references (will be set by main app)
simulation_engine = None
ai_brain = None
websocket_manager = None

def set_global_instances(sim_engine, ai_brain_instance, ws_manager):
    """Set global instances for API access"""
    global simulation_engine, ai_brain, websocket_manager
    simulation_engine = sim_engine
    ai_brain = ai_brain_instance
    websocket_manager = ws_manager

@api_router.get("/section/state", response_model=Dict[str, Any])
async def get_section_state():
    """Get current railway section state"""
    try:
        if simulation_engine:
            state = simulation_engine.get_current_state()
            return {
                "status": "success",
                "data": {
                    "section_id": state.section_id,
                    "trains": [
                        {
                            "id": train.id,
                            "number": train.number,
                            "name": train.name,
                            "type": train.type.value,
                            "current_km": train.current_km,
                            "current_speed": train.current_speed,
                            "status": train.status.value,
                            "delay_minutes": train.delay_minutes,
                            "priority": train.priority
                        }
                        for train in state.trains
                    ],
                    "disruptions": [
                        {
                            "id": disruption.id,
                            "type": disruption.type.value,
                            "description": disruption.description,
                            "location_km": disruption.location_km,
                            "severity": disruption.severity,
                            "is_resolved": disruption.is_resolved
                        }
                        for disruption in state.disruptions
                    ],
                    "performance": {
                        "throughput_trains_per_hour": state.throughput_trains_per_hour,
                        "on_time_percentage": state.on_time_percentage,
                        "average_delay_minutes": state.average_delay_minutes,
                        "total_trains_today": state.total_trains_today
                    },
                    "last_updated": state.last_updated.isoformat()
                }
            }
        else:
            return {"status": "error", "message": "Simulation engine not available"}
    except Exception as e:
        logger.error(f"Error getting section state: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.get("/trains", response_model=Dict[str, Any])
async def get_all_trains():
    """Get all trains in the system"""
    try:
        if simulation_engine:
            state = simulation_engine.get_current_state()
            return {
                "status": "success",
                "data": {
                    "trains": [
                        {
                            "id": train.id,
                            "number": train.number,
                            "name": train.name,
                            "type": train.type.value,
                            "current_km": train.current_km,
                            "current_speed": train.current_speed,
                            "status": train.status.value,
                            "delay_minutes": train.delay_minutes,
                            "priority": train.priority,
                            "origin": train.origin,
                            "destination": train.destination,
                            "scheduled_departure": train.scheduled_departure.isoformat() if train.scheduled_departure else None,
                            "scheduled_arrival": train.scheduled_arrival.isoformat() if train.scheduled_arrival else None
                        }
                        for train in state.trains
                    ],
                    "count": len(state.trains)
                }
            }
        else:
            return {"status": "error", "message": "Simulation engine not available"}
    except Exception as e:
        logger.error(f"Error getting trains: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.get("/trains/{train_id}", response_model=Dict[str, Any])
async def get_train_details(train_id: str):
    """Get detailed information for a specific train"""
    try:
        if simulation_engine:
            state = simulation_engine.get_current_state()
            train = next((t for t in state.trains if t.id == train_id), None)
            
            if train:
                return {
                    "status": "success",
                    "data": {
                        "id": train.id,
                        "number": train.number,
                        "name": train.name,
                        "type": train.type.value,
                        "current_km": train.current_km,
                        "current_speed": train.current_speed,
                        "status": train.status.value,
                        "delay_minutes": train.delay_minutes,
                        "priority": train.priority,
                        "origin": train.origin,
                        "destination": train.destination,
                        "max_speed": train.max_speed,
                        "length_meters": train.length_meters,
                        "weight_tons": train.weight_tons,
                        "scheduled_departure": train.scheduled_departure.isoformat() if train.scheduled_departure else None,
                        "scheduled_arrival": train.scheduled_arrival.isoformat() if train.scheduled_arrival else None,
                        "actual_departure": train.actual_departure.isoformat() if train.actual_departure else None,
                        "actual_arrival": train.actual_arrival.isoformat() if train.actual_arrival else None
                    }
                }
            else:
                raise HTTPException(status_code=404, detail="Train not found")
        else:
            return {"status": "error", "message": "Simulation engine not available"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting train details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.post("/disruption/inject", response_model=Dict[str, Any])
async def inject_disruption(disruption_data: Dict[str, Any], background_tasks: BackgroundTasks):
    """Inject a disruption for demo purposes"""
    try:
        if simulation_engine:
            # Inject disruption
            await simulation_engine.inject_disruption(disruption_data)
            
            # Trigger AI response in background
            if ai_brain:
                background_tasks.add_task(handle_disruption_ai_response, disruption_data)
            
            return {
                "status": "success",
                "message": "Disruption injected successfully",
                "data": disruption_data
            }
        else:
            return {"status": "error", "message": "Simulation engine not available"}
    except Exception as e:
        logger.error(f"Error injecting disruption: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def handle_disruption_ai_response(disruption_data: Dict[str, Any]):
    """Handle AI response to disruption (background task)"""
    try:
        if ai_brain and simulation_engine:
            # Create disruption object
            from ..models.railway_models import Disruption, DisruptionType
            
            disruption = Disruption(
                type=DisruptionType.ENGINE_FAILURE,
                description=disruption_data.get('description', 'Engine failure'),
                location_km=disruption_data.get('location_km', 45),
                track_id="main_line",
                severity=disruption_data.get('severity', 4),
                estimated_duration_minutes=disruption_data.get('duration', 30)
            )
            
            # Get AI recommendations
            recommendations = await ai_brain.handle_disruption(disruption)
            
            # Broadcast recommendations via WebSocket
            if websocket_manager:
                await websocket_manager.broadcast_recommendation({
                    "disruption_id": disruption.id,
                    "recommendations": [
                        {
                            "id": rec.id,
                            "type": rec.type,
                            "train_id": rec.train_id,
                            "action": rec.action,
                            "reason": rec.reason,
                            "confidence": rec.confidence,
                            "estimated_impact": rec.estimated_impact
                        }
                        for rec in recommendations
                    ]
                })
    except Exception as e:
        logger.error(f"Error in AI disruption response: {e}")

@api_router.get("/recommendations", response_model=Dict[str, Any])
async def get_recommendations():
    """Get current AI recommendations"""
    try:
        if simulation_engine:
            state = simulation_engine.get_current_state()
            return {
                "status": "success",
                "data": {
                    "recommendations": [
                        {
                            "id": rec.id,
                            "type": rec.type,
                            "train_id": rec.train_id,
                            "action": rec.action,
                            "reason": rec.reason,
                            "confidence": rec.confidence,
                            "estimated_impact": rec.estimated_impact,
                            "created_at": rec.created_at.isoformat(),
                            "is_executed": rec.is_executed,
                            "is_approved": rec.is_approved
                        }
                        for rec in state.recommendations
                    ],
                    "count": len(state.recommendations)
                }
            }
        else:
            return {"status": "error", "message": "Simulation engine not available"}
    except Exception as e:
        logger.error(f"Error getting recommendations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.post("/simulation/what-if", response_model=Dict[str, Any])
async def create_what_if_scenario(scenario_data: Dict[str, Any]):
    """Create and run a what-if simulation scenario"""
    try:
        if simulation_engine:
            scenario_id = f"scenario_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            modifications = scenario_data.get('modifications', [])
            
            # Create scenario
            simulation_engine.create_what_if_scenario(scenario_id, modifications)
            
            # Run scenario
            duration = scenario_data.get('duration_minutes', 60)
            results = simulation_engine.run_what_if_scenario(scenario_id, duration)
            
            return {
                "status": "success",
                "data": results
            }
        else:
            return {"status": "error", "message": "Simulation engine not available"}
    except Exception as e:
        logger.error(f"Error creating what-if scenario: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.get("/performance/metrics", response_model=Dict[str, Any])
async def get_performance_metrics():
    """Get system performance metrics"""
    try:
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "simulation": {},
            "ai": {},
            "system": {}
        }
        
        if simulation_engine:
            state = simulation_engine.get_current_state()
            metrics["simulation"] = {
                "throughput_trains_per_hour": state.throughput_trains_per_hour,
                "on_time_percentage": state.on_time_percentage,
                "average_delay_minutes": state.average_delay_minutes,
                "total_trains": len(state.trains),
                "active_disruptions": len(state.disruptions)
            }
        
        if ai_brain:
            ai_metrics = ai_brain.get_performance_metrics()
            metrics["ai"] = ai_metrics
        
        if websocket_manager:
            metrics["system"] = {
                "websocket_connections": websocket_manager.get_connection_count(),
                "api_status": "operational"
            }
        
        return {
            "status": "success",
            "data": metrics
        }
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.get("/system/status", response_model=Dict[str, Any])
async def get_system_status():
    """Get overall system status"""
    try:
        status = {
            "system": "SAMAY Railway Traffic Management",
            "version": settings.VERSION,
            "status": "operational",
            "components": {
                "simulation_engine": simulation_engine is not None,
                "ai_brain": ai_brain is not None,
                "websocket_manager": websocket_manager is not None
            },
            "timestamp": datetime.now().isoformat()
        }
        
        if websocket_manager:
            status["websocket_connections"] = websocket_manager.get_connection_count()
        
        return {
            "status": "success",
            "data": status
        }
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
