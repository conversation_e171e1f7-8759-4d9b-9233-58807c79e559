"""
SAMAY Railway Data Models
Core data structures for the railway traffic management system
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
import uuid

class TrainType(str, Enum):
    EXPRESS = "EXPRESS"
    PASSENGER = "PASSENGER"
    FREIGHT = "FREIGHT"
    LOCAL = "LOCAL"

class TrainStatus(str, Enum):
    ON_TIME = "ON_TIME"
    DELAYED = "DELAYED"
    AHEAD = "AHEAD"
    STOPPED = "STOPPED"
    CANCELLED = "CANCELLED"

class SignalState(str, Enum):
    RED = "RED"
    YELLOW = "YELLOW"
    GREEN = "GREEN"
    DOUBLE_YELLOW = "DOUBLE_YELLOW"

class DisruptionType(str, Enum):
    ENGINE_FAILURE = "ENGINE_FAILURE"
    TRACK_MAINTENANCE = "TRACK_MAINTENANCE"
    SIGNAL_FAILURE = "SIGNAL_FAILURE"
    WEATHER = "WEATHER"
    ACCIDENT = "ACCIDENT"

class Station(BaseModel):
    """Railway station model"""
    id: str
    name: str
    km_position: float
    platforms: int
    is_junction: bool = False
    coordinates: Optional[Dict[str, float]] = None

class Track(BaseModel):
    """Railway track segment model"""
    id: str
    start_station: str
    end_station: str
    length_km: float
    is_bidirectional: bool = True
    max_speed_kmh: int = 160
    gradient: float = 0.0
    is_electrified: bool = True

class Signal(BaseModel):
    """Railway signal model"""
    id: str
    km_position: float
    track_id: str
    state: SignalState = SignalState.RED
    authority: str = "STOP"
    last_updated: datetime = Field(default_factory=datetime.now)

class Train(BaseModel):
    """Train model with real-time data"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    number: str
    name: str
    type: TrainType
    priority: int = Field(ge=1, le=5)
    
    # Current state
    current_km: float = 0.0
    current_speed: float = 0.0
    status: TrainStatus = TrainStatus.ON_TIME
    direction: str = "UP"  # UP or DOWN
    
    # Schedule
    origin: str
    destination: str
    scheduled_departure: datetime
    scheduled_arrival: datetime
    
    # Performance
    actual_departure: Optional[datetime] = None
    actual_arrival: Optional[datetime] = None
    delay_minutes: int = 0
    
    # Technical
    max_speed: int = 160
    length_meters: int = 200
    weight_tons: int = 500
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class Schedule(BaseModel):
    """Train schedule entry"""
    train_id: str
    station_id: str
    scheduled_arrival: Optional[datetime] = None
    scheduled_departure: Optional[datetime] = None
    actual_arrival: Optional[datetime] = None
    actual_departure: Optional[datetime] = None
    platform: Optional[int] = None
    
class Disruption(BaseModel):
    """Disruption event model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: DisruptionType
    description: str
    location_km: float
    track_id: str
    severity: int = Field(ge=1, le=5)  # 1=minor, 5=critical
    start_time: datetime = Field(default_factory=datetime.now)
    estimated_duration_minutes: int = 30
    affected_trains: List[str] = []
    is_resolved: bool = False

class Recommendation(BaseModel):
    """AI recommendation model"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    type: str  # REROUTE, HOLD, PRIORITIZE, SPEED_ADJUST
    train_id: str
    action: str
    reason: str
    confidence: float = Field(ge=0.0, le=1.0)
    estimated_impact: Dict[str, Any] = {}
    created_at: datetime = Field(default_factory=datetime.now)
    is_executed: bool = False
    is_approved: bool = False

class SectionState(BaseModel):
    """Current state of a railway section"""
    section_id: str
    trains: List[Train] = []
    signals: List[Signal] = []
    disruptions: List[Disruption] = []
    recommendations: List[Recommendation] = []
    
    # Performance metrics
    throughput_trains_per_hour: float = 0.0
    on_time_percentage: float = 100.0
    average_delay_minutes: float = 0.0
    total_trains_today: int = 0
    
    last_updated: datetime = Field(default_factory=datetime.now)

class SimulationScenario(BaseModel):
    """What-if simulation scenario"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    base_state: SectionState
    modifications: List[Dict[str, Any]] = []
    results: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=datetime.now)

class PerformanceMetrics(BaseModel):
    """System performance metrics"""
    timestamp: datetime = Field(default_factory=datetime.now)
    section_id: str
    
    # Throughput metrics
    trains_processed: int = 0
    trains_per_hour: float = 0.0
    
    # Delay metrics
    total_delay_minutes: float = 0.0
    average_delay_minutes: float = 0.0
    on_time_trains: int = 0
    delayed_trains: int = 0
    
    # Efficiency metrics
    track_utilization_percentage: float = 0.0
    platform_utilization_percentage: float = 0.0
    energy_efficiency_score: float = 0.0
    
    # AI metrics
    recommendations_generated: int = 0
    recommendations_accepted: int = 0
    ai_accuracy_percentage: float = 0.0
