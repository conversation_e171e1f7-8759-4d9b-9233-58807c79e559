<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAMAY - Railway Traffic Management System</title>
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <h1><i class="fas fa-train"></i> SAMAY</h1>
                <span class="subtitle">AI-Powered Railway Traffic Management</span>
            </div>
            <div class="header-right">
                <div class="system-status" id="systemStatus">
                    <i class="fas fa-circle status-indicator"></i>
                    <span>System Operational</span>
                </div>
                <div class="current-time" id="currentTime"></div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="dashboard-main">
            <!-- Left Panel - Network Visualization -->
            <div class="left-panel">
                <div class="panel-header">
                    <h2><i class="fas fa-route"></i> Network Overview</h2>
                    <div class="panel-controls">
                        <button class="btn btn-primary" id="injectDisruption">
                            <i class="fas fa-exclamation-triangle"></i> Inject Disruption
                        </button>
                        <button class="btn btn-secondary" id="whatIfBtn">
                            <i class="fas fa-flask"></i> What-If Analysis
                        </button>
                    </div>
                </div>
                
                <!-- Railway Network Map -->
                <div class="network-map" id="networkMap">
                    <svg id="railwayNetwork" width="100%" height="400">
                        <!-- Railway tracks will be drawn here -->
                    </svg>
                </div>

                <!-- Train List -->
                <div class="train-list">
                    <h3><i class="fas fa-list"></i> Active Trains</h3>
                    <div class="train-items" id="trainList">
                        <!-- Train items will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Right Panel - KPIs and Controls -->
            <div class="right-panel">
                <!-- Performance KPIs -->
                <div class="kpi-section">
                    <h2><i class="fas fa-chart-line"></i> Performance Metrics</h2>
                    <div class="kpi-grid">
                        <div class="kpi-card">
                            <div class="kpi-value" id="throughputKPI">12</div>
                            <div class="kpi-label">Trains/Hour</div>
                            <div class="kpi-trend positive">
                                <i class="fas fa-arrow-up"></i> +5%
                            </div>
                        </div>
                        <div class="kpi-card">
                            <div class="kpi-value" id="onTimeKPI">98</div>
                            <div class="kpi-label">On-Time %</div>
                            <div class="kpi-trend positive">
                                <i class="fas fa-arrow-up"></i> +2%
                            </div>
                        </div>
                        <div class="kpi-card">
                            <div class="kpi-value" id="delayKPI">1.2</div>
                            <div class="kpi-label">Avg Delay (min)</div>
                            <div class="kpi-trend negative">
                                <i class="fas fa-arrow-down"></i> -0.3
                            </div>
                        </div>
                        <div class="kpi-card">
                            <div class="kpi-value" id="conflictsKPI">0</div>
                            <div class="kpi-label">Conflicts</div>
                            <div class="kpi-trend neutral">
                                <i class="fas fa-minus"></i> 0
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Recommendations -->
                <div class="recommendations-section">
                    <h2><i class="fas fa-brain"></i> AI Recommendations</h2>
                    <div class="recommendations-list" id="recommendationsList">
                        <div class="recommendation-item">
                            <div class="rec-header">
                                <span class="rec-type">OPTIMIZE</span>
                                <span class="rec-confidence">95%</span>
                            </div>
                            <div class="rec-content">
                                System operating optimally. No immediate actions required.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Disruption Alerts -->
                <div class="alerts-section">
                    <h2><i class="fas fa-exclamation-triangle"></i> Active Alerts</h2>
                    <div class="alerts-list" id="alertsList">
                        <div class="alert-item info">
                            <i class="fas fa-info-circle"></i>
                            <span>System monitoring active - All systems normal</span>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="system-info">
                    <h3><i class="fas fa-cog"></i> System Status</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">AI Engine:</span>
                            <span class="info-value" id="aiStatus">Active</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Simulation:</span>
                            <span class="info-value" id="simStatus">Running</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Connections:</span>
                            <span class="info-value" id="connectionCount">1</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Last Update:</span>
                            <span class="info-value" id="lastUpdate">Just now</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Disruption Modal -->
    <div class="modal" id="disruptionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Inject Disruption</h3>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Disruption Type:</label>
                    <select id="disruptionType">
                        <option value="ENGINE_FAILURE">Engine Failure</option>
                        <option value="TRACK_MAINTENANCE">Track Maintenance</option>
                        <option value="SIGNAL_FAILURE">Signal Failure</option>
                        <option value="WEATHER">Weather Condition</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Location (km):</label>
                    <input type="number" id="disruptionLocation" value="45" min="0" max="150">
                </div>
                <div class="form-group">
                    <label>Severity (1-5):</label>
                    <input type="range" id="disruptionSeverity" min="1" max="5" value="4">
                    <span id="severityValue">4</span>
                </div>
                <div class="form-group">
                    <label>Duration (minutes):</label>
                    <input type="number" id="disruptionDuration" value="30" min="5" max="120">
                </div>
                <div class="form-group">
                    <label>Description:</label>
                    <textarea id="disruptionDescription" placeholder="Describe the disruption...">Locomotive failure blocking main line</textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelDisruption">Cancel</button>
                <button class="btn btn-danger" id="confirmDisruption">Inject Disruption</button>
            </div>
        </div>
    </div>

    <!-- What-If Modal -->
    <div class="modal" id="whatIfModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-flask"></i> What-If Analysis</h3>
                <button class="modal-close" id="closeWhatIfModal">&times;</button>
            </div>
            <div class="modal-body">
                <p>Create a simulation scenario to test different strategies:</p>
                <div class="form-group">
                    <label>Scenario Name:</label>
                    <input type="text" id="scenarioName" placeholder="e.g., Priority Express Train">
                </div>
                <div class="form-group">
                    <label>Modifications:</label>
                    <div class="modification-list" id="modificationList">
                        <div class="modification-item">
                            <select class="mod-type">
                                <option value="delay_train">Delay Train</option>
                                <option value="priority_change">Change Priority</option>
                                <option value="route_change">Route Change</option>
                            </select>
                            <input type="text" class="mod-train" placeholder="Train ID">
                            <input type="number" class="mod-value" placeholder="Value">
                        </div>
                    </div>
                    <button class="btn btn-small" id="addModification">Add Modification</button>
                </div>
                <div class="form-group">
                    <label>Simulation Duration (minutes):</label>
                    <input type="number" id="simulationDuration" value="60" min="10" max="240">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelWhatIf">Cancel</button>
                <button class="btn btn-primary" id="runWhatIf">Run Simulation</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/static/js/dashboard.js"></script>
    <script src="/static/js/network-visualization.js"></script>
    <script src="/static/js/websocket-client.js"></script>
</body>
</html>
