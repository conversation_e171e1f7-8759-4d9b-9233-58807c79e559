"""
SAMAY Reinforcement Learning Agent
Multi-Agent RL system for real-time train control decisions
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from collections import deque
import random
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import logging

from .config import settings

logger = logging.getLogger(__name__)

@dataclass
class RLAction:
    """RL Action with metadata"""
    action_id: int
    confidence: float
    reasoning: str
    expected_delay_change: float
    throughput_impact: float
    safety_score: float

class DQNNetwork(nn.Module):
    """Deep Q-Network for train control decisions"""
    
    def __init__(self, state_size: int = 100, action_size: int = 5, hidden_size: int = 256):
        super(DQNNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Linear(state_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.<PERSON>(),
            nn.Linear(hidden_size // 2, action_size)
        )
        
        # Value and advantage streams for Dueling DQN
        self.value_stream = nn.Sequential(
            nn.Linear(hidden_size // 2, 1)
        )
        
        self.advantage_stream = nn.Sequential(
            nn.Linear(hidden_size // 2, action_size)
        )
    
    def forward(self, x):
        """Forward pass through the network"""
        features = self.network[:-1](x)  # All layers except the last
        
        # Dueling DQN architecture
        value = self.value_stream(features)
        advantage = self.advantage_stream(features)
        
        # Combine value and advantage
        q_values = value + (advantage - advantage.mean(dim=1, keepdim=True))
        
        return q_values

class ReplayBuffer:
    """Experience replay buffer for training"""
    
    def __init__(self, capacity: int = 100000):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        """Add experience to buffer"""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int) -> Tuple:
        """Sample batch of experiences"""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)

class RLAgent:
    """
    Multi-Agent Reinforcement Learning system for train control
    Uses Deep Q-Learning with experience replay and target networks
    """
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Network parameters
        self.state_size = 100
        self.action_size = 5
        self.learning_rate = settings.RL_LEARNING_RATE
        self.batch_size = settings.RL_BATCH_SIZE
        
        # Networks
        self.q_network = DQNNetwork(self.state_size, self.action_size).to(self.device)
        self.target_network = DQNNetwork(self.state_size, self.action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=self.learning_rate)
        
        # Experience replay
        self.memory = ReplayBuffer(settings.RL_MEMORY_SIZE)
        
        # Training parameters
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.gamma = 0.95  # Discount factor
        self.tau = 0.005  # Soft update parameter
        self.update_frequency = 4
        self.target_update_frequency = 100
        
        # Training state
        self.step_count = 0
        self.episode_count = 0
        self.total_reward = 0
        
        # Action definitions
        self.action_meanings = {
            0: "PROCEED_NORMAL",
            1: "PROCEED_SLOW",
            2: "HOLD_SIGNAL", 
            3: "REROUTE",
            4: "PRIORITY_BOOST"
        }
        
        self.action_descriptions = {
            0: "Proceed at normal speed - optimal flow",
            1: "Proceed at reduced speed - safety priority",
            2: "Hold at signal - traffic management",
            3: "Take alternate route - avoid congestion",
            4: "Grant priority passage - minimize delay"
        }
        
        logger.info(f"🤖 RL Agent initialized on {self.device}")
    
    async def get_actions(self, state_vector: np.ndarray) -> Dict[str, RLAction]:
        """Get actions for all active trains"""
        # For demo, we'll simulate multiple train decisions
        actions = {}
        
        # Convert state to tensor
        state_tensor = torch.FloatTensor(state_vector).unsqueeze(0).to(self.device)
        
        # Get Q-values
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
        
        # Epsilon-greedy action selection
        if random.random() < self.epsilon:
            action_id = random.randint(0, self.action_size - 1)
            confidence = 0.3  # Low confidence for random actions
        else:
            action_id = q_values.argmax().item()
            confidence = torch.softmax(q_values, dim=1).max().item()
        
        # Create action for primary train (simplified)
        primary_action = self._create_rl_action(action_id, confidence, state_vector)
        actions["primary_train"] = primary_action
        
        # Generate actions for other trains (simplified)
        for i in range(min(3, int(state_vector[60] * 50))):  # Based on train count
            train_id = f"train_{i+1}"
            # Slightly different action for variety
            alt_action_id = (action_id + i) % self.action_size
            alt_confidence = max(0.4, confidence - i * 0.1)
            actions[train_id] = self._create_rl_action(alt_action_id, alt_confidence, state_vector)
        
        return actions
    
    def _create_rl_action(self, action_id: int, confidence: float, state: np.ndarray) -> RLAction:
        """Create RLAction with reasoning and impact estimates"""
        
        # Estimate impacts based on action and state
        delay_changes = {
            0: 0.0,      # Normal - no change
            1: 2.0,      # Slow - slight delay
            2: 5.0,      # Hold - moderate delay
            3: -3.0,     # Reroute - potential improvement
            4: -8.0      # Priority - significant improvement
        }
        
        throughput_impacts = {
            0: 0.0,      # Normal - no change
            1: -0.1,     # Slow - slight reduction
            2: -0.3,     # Hold - moderate reduction
            3: 0.1,      # Reroute - potential improvement
            4: 0.2       # Priority - improvement
        }
        
        safety_scores = {
            0: 0.8,      # Normal - good safety
            1: 0.95,     # Slow - very safe
            2: 0.9,      # Hold - safe
            3: 0.75,     # Reroute - slightly less safe
            4: 0.7       # Priority - requires attention
        }
        
        # Generate reasoning based on action and state
        reasoning = self._generate_reasoning(action_id, state)
        
        return RLAction(
            action_id=action_id,
            confidence=confidence,
            reasoning=reasoning,
            expected_delay_change=delay_changes.get(action_id, 0.0),
            throughput_impact=throughput_impacts.get(action_id, 0.0),
            safety_score=safety_scores.get(action_id, 0.8)
        )
    
    def _generate_reasoning(self, action_id: int, state: np.ndarray) -> str:
        """Generate human-readable reasoning for the action"""
        
        # Extract key state features
        avg_speed = state[1] if len(state) > 1 else 0.5
        delay_level = state[2] if len(state) > 2 else 0.0
        train_density = state[60] if len(state) > 60 else 0.5
        on_time_perf = state[62] if len(state) > 62 else 0.9
        
        base_reasons = {
            0: "Current conditions are optimal for normal operation",
            1: "High traffic density detected, reducing speed for safety",
            2: "Network congestion ahead, holding to optimize flow",
            3: "Alternative route available with better conditions",
            4: "High-priority train requires expedited passage"
        }
        
        # Add context-specific reasoning
        context_additions = []
        
        if delay_level > 0.3:
            context_additions.append("significant delays detected")
        if train_density > 0.7:
            context_additions.append("high traffic volume")
        if on_time_perf < 0.8:
            context_additions.append("performance below target")
        if avg_speed < 0.3:
            context_additions.append("slow movement detected")
        
        base_reason = base_reasons.get(action_id, "Optimizing traffic flow")
        
        if context_additions:
            return f"{base_reason} - {', '.join(context_additions)}"
        else:
            return base_reason
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay buffer"""
        self.memory.push(state, action, reward, next_state, done)
    
    async def update_learning(self):
        """Update the neural network with experience replay"""
        if len(self.memory) < self.batch_size:
            return
        
        # Sample batch from memory
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)
        
        # Convert to tensors
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.BoolTensor(dones).to(self.device)
        
        # Current Q values
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # Next Q values from target network
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
        
        # Compute loss
        loss = nn.MSELoss()(current_q_values.squeeze(), target_q_values)
        
        # Optimize
        self.optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.q_network.parameters(), 1.0)
        self.optimizer.step()
        
        # Update target network
        self.step_count += 1
        if self.step_count % self.target_update_frequency == 0:
            self._soft_update_target_network()
        
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        logger.debug(f"RL learning update - Loss: {loss.item():.4f}, Epsilon: {self.epsilon:.4f}")
    
    def _soft_update_target_network(self):
        """Soft update of target network parameters"""
        for target_param, local_param in zip(self.target_network.parameters(), 
                                           self.q_network.parameters()):
            target_param.data.copy_(
                self.tau * local_param.data + (1.0 - self.tau) * target_param.data
            )
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get RL agent performance metrics"""
        return {
            "episodes_trained": self.episode_count,
            "steps_taken": self.step_count,
            "epsilon": self.epsilon,
            "memory_size": len(self.memory),
            "average_reward": self.total_reward / max(self.episode_count, 1),
            "learning_rate": self.learning_rate
        }
    
    def save_model(self, filepath: str):
        """Save the trained model"""
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'step_count': self.step_count,
            'episode_count': self.episode_count
        }, filepath)
        logger.info(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str):
        """Load a trained model"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
        self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.epsilon = checkpoint['epsilon']
        self.step_count = checkpoint['step_count']
        self.episode_count = checkpoint['episode_count']
        logger.info(f"Model loaded from {filepath}")
    
    def set_training_mode(self, training: bool = True):
        """Set training mode"""
        if training:
            self.q_network.train()
            self.target_network.train()
        else:
            self.q_network.eval()
            self.target_network.eval()
