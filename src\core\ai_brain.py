"""
SAMAY AI Brain - The Core Intelligence System
Hybrid AI approach combining RL, CP-SAT, and GA for optimal train scheduling
"""

import asyncio
import numpy as np
import torch
import torch.nn as nn
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

from ..models.railway_models import (
    Train, Disruption, Recommendation, SectionState, 
    TrainType, DisruptionType
)
from .optimization_engine import OptimizationEngine
from .rl_agent import RLAgent
from .genetic_algorithm import GeneticAlgorithm

logger = logging.getLogger(__name__)

@dataclass
class AIDecision:
    """AI decision with confidence and reasoning"""
    action: str
    train_id: str
    confidence: float
    reasoning: str
    expected_impact: Dict[str, float]

class AIBrain:
    """
    The central AI intelligence that coordinates all optimization algorithms
    """
    
    def __init__(self):
        self.optimization_engine = OptimizationEngine()
        self.rl_agent = RLAgent()
        self.genetic_algorithm = GeneticAlgorithm()
        
        # State management
        self.current_state: Optional[SectionState] = None
        self.baseline_schedule: Optional[Dict] = None
        self.is_running = False
        
        # Performance tracking
        self.decisions_made = 0
        self.successful_predictions = 0
        self.learning_rate = 0.001
        
        logger.info("🧠 AI Brain initialized with hybrid algorithms")
    
    async def start_optimization_loop(self):
        """Start the main AI optimization loop"""
        self.is_running = True
        logger.info("🚀 Starting AI optimization loop...")
        
        while self.is_running:
            try:
                if self.current_state:
                    # Generate baseline schedule every 15 minutes
                    if self._should_regenerate_baseline():
                        await self._generate_baseline_schedule()
                    
                    # Real-time RL decisions
                    await self._make_realtime_decisions()
                    
                    # Update learning
                    await self._update_learning()
                
                await asyncio.sleep(1)  # 1-second decision cycle
                
            except Exception as e:
                logger.error(f"Error in AI optimization loop: {e}")
                await asyncio.sleep(5)
    
    async def handle_disruption(self, disruption: Disruption) -> List[Recommendation]:
        """Handle major disruptions using GA for rapid recovery"""
        logger.info(f"🚨 Handling disruption: {disruption.type} at km {disruption.location_km}")
        
        # Use Genetic Algorithm for rapid recovery planning
        recovery_plan = await self.genetic_algorithm.generate_recovery_plan(
            disruption, self.current_state
        )
        
        # Convert to recommendations
        recommendations = []
        for action in recovery_plan.actions:
            rec = Recommendation(
                type=action.type,
                train_id=action.train_id,
                action=action.description,
                reason=f"Disruption recovery: {action.reasoning}",
                confidence=action.confidence,
                estimated_impact=action.impact
            )
            recommendations.append(rec)
        
        # Update baseline schedule with recovery plan
        self.baseline_schedule = recovery_plan.new_schedule
        
        logger.info(f"✅ Generated {len(recommendations)} recovery recommendations")
        return recommendations
    
    async def _generate_baseline_schedule(self):
        """Generate optimal baseline schedule using CP-SAT"""
        logger.info("📊 Generating baseline schedule with CP-SAT...")
        
        try:
            # Use constraint programming for optimal scheduling
            schedule = await self.optimization_engine.solve_scheduling_problem(
                trains=self.current_state.trains,
                constraints=self._get_current_constraints(),
                time_horizon_hours=2
            )
            
            self.baseline_schedule = schedule
            logger.info("✅ Baseline schedule generated successfully")
            
        except Exception as e:
            logger.error(f"Failed to generate baseline schedule: {e}")
    
    async def _make_realtime_decisions(self):
        """Make real-time decisions using RL agent"""
        if not self.baseline_schedule:
            return
        
        # Get current state for RL agent
        state_vector = self._encode_state_for_rl()
        
        # Get RL agent decisions
        actions = await self.rl_agent.get_actions(state_vector)
        
        # Process actions and create recommendations
        for train_id, action in actions.items():
            if action.confidence > 0.7:  # Only high-confidence decisions
                recommendation = self._create_recommendation_from_action(
                    train_id, action
                )
                
                # Add to current state recommendations
                if self.current_state:
                    self.current_state.recommendations.append(recommendation)
    
    def _encode_state_for_rl(self) -> np.ndarray:
        """Encode current railway state for RL agent"""
        if not self.current_state:
            return np.zeros(100)  # Default state vector
        
        state_features = []
        
        # Train features
        for train in self.current_state.trains[:10]:  # Limit to 10 trains
            features = [
                train.current_km / 150.0,  # Normalized position
                train.current_speed / 160.0,  # Normalized speed
                train.delay_minutes / 60.0,  # Normalized delay
                train.priority / 5.0,  # Normalized priority
                1.0 if train.type == TrainType.EXPRESS else 0.0,
                1.0 if train.type == TrainType.FREIGHT else 0.0
            ]
            state_features.extend(features)
        
        # Pad to fixed size
        while len(state_features) < 60:
            state_features.append(0.0)
        
        # Section features
        section_features = [
            len(self.current_state.trains) / 50.0,  # Normalized train count
            len(self.current_state.disruptions) / 10.0,  # Normalized disruption count
            self.current_state.on_time_percentage / 100.0,
            self.current_state.average_delay_minutes / 30.0
        ]
        
        state_features.extend(section_features)
        
        # Pad to 100 features
        while len(state_features) < 100:
            state_features.append(0.0)
        
        return np.array(state_features[:100], dtype=np.float32)
    
    def _create_recommendation_from_action(self, train_id: str, action) -> Recommendation:
        """Convert RL action to recommendation"""
        action_types = {
            0: "PROCEED_NORMAL",
            1: "PROCEED_SLOW", 
            2: "HOLD_SIGNAL",
            3: "REROUTE",
            4: "PRIORITY_BOOST"
        }
        
        action_descriptions = {
            0: "Proceed at normal speed",
            1: "Proceed at reduced speed for safety",
            2: "Hold at next signal for traffic management",
            3: "Reroute via alternate path",
            4: "Grant priority passage"
        }
        
        return Recommendation(
            type=action_types.get(action.action_id, "PROCEED_NORMAL"),
            train_id=train_id,
            action=action_descriptions.get(action.action_id, "Proceed normally"),
            reason=action.reasoning,
            confidence=action.confidence,
            estimated_impact={
                "delay_change_minutes": action.expected_delay_change,
                "throughput_impact": action.throughput_impact,
                "safety_score": action.safety_score
            }
        )
    
    def _get_current_constraints(self) -> Dict[str, Any]:
        """Get current operational constraints"""
        return {
            "safety_headway_minutes": 3,
            "max_platform_occupancy": 2,
            "track_capacity": 1,
            "signal_constraints": True,
            "priority_weights": {
                TrainType.EXPRESS: 1.0,
                TrainType.PASSENGER: 0.8,
                TrainType.LOCAL: 0.6,
                TrainType.FREIGHT: 0.4
            }
        }
    
    def _should_regenerate_baseline(self) -> bool:
        """Check if baseline schedule should be regenerated"""
        if not self.baseline_schedule:
            return True
        
        # Regenerate every 15 minutes or if major disruption
        last_generation = self.baseline_schedule.get("generated_at", datetime.min)
        time_since_generation = datetime.now() - last_generation
        
        return (
            time_since_generation > timedelta(minutes=15) or
            len(self.current_state.disruptions) > 0
        )
    
    async def _update_learning(self):
        """Update RL agent learning based on outcomes"""
        # This would implement the learning update logic
        # For now, we'll simulate learning updates
        self.decisions_made += 1
        
        # Simulate learning every 100 decisions
        if self.decisions_made % 100 == 0:
            await self.rl_agent.update_learning()
            logger.info(f"🎓 RL agent learning updated after {self.decisions_made} decisions")
    
    def update_state(self, new_state: SectionState):
        """Update the current railway state"""
        self.current_state = new_state
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get AI performance metrics"""
        accuracy = (
            self.successful_predictions / max(self.decisions_made, 1) * 100
        )
        
        return {
            "decisions_made": self.decisions_made,
            "accuracy_percentage": accuracy,
            "learning_rate": self.learning_rate,
            "baseline_schedule_age_minutes": self._get_baseline_age_minutes(),
            "active_algorithms": ["RL", "CP-SAT", "GA"]
        }
    
    def _get_baseline_age_minutes(self) -> float:
        """Get age of current baseline schedule in minutes"""
        if not self.baseline_schedule:
            return 0.0
        
        generated_at = self.baseline_schedule.get("generated_at", datetime.now())
        age = datetime.now() - generated_at
        return age.total_seconds() / 60.0
    
    async def stop(self):
        """Stop the AI brain"""
        self.is_running = False
        logger.info("🛑 AI Brain stopped")
