"""
SAMAY Simulation Engine
Digital Twin for railway network simulation and what-if analysis
"""

import asyncio
import simpy
import numpy as np
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
import logging
import json
from dataclasses import dataclass, asdict

from ..models.railway_models import (
    Train, Station, Track, Signal, Disruption, SectionState,
    TrainType, TrainStatus, SignalState, DisruptionType
)
from .config import settings, STATIONS, TRAIN_TYPES

logger = logging.getLogger(__name__)

@dataclass
class SimulationEvent:
    """Simulation event for logging and analysis"""
    timestamp: float
    event_type: str
    train_id: str
    location: str
    details: Dict[str, Any]

class TrainSimulation:
    """Individual train simulation within the environment"""
    
    def __init__(self, env: simpy.Environment, train: Train, network: 'NetworkSimulation'):
        self.env = env
        self.train = train
        self.network = network
        self.current_station_idx = 0
        self.is_moving = True
        self.events = []
        
    def run(self):
        """Main train simulation process"""
        try:
            stations = STATIONS[:6]  # Simulate first 6 stations
            
            for i, station in enumerate(stations):
                if i == 0:
                    # Starting station
                    yield self.env.timeout(0)
                    self._log_event("DEPARTURE", station['id'], {"platform": 1})
                else:
                    # Travel to next station
                    travel_time = self._calculate_travel_time(stations[i-1], station)
                    
                    # Check for disruptions during travel
                    yield from self._travel_with_disruption_check(travel_time, station)
                    
                    # Arrive at station
                    self._log_event("ARRIVAL", station['id'], {"delay": self.train.delay_minutes})
                    
                    # Stop at station
                    stop_time = self._calculate_stop_time(station)
                    yield self.env.timeout(stop_time)
                    
                    # Depart from station
                    self._log_event("DEPARTURE", station['id'], {"platform": 1})
                
                # Update train position
                self.train.current_km = station['km']
                self.current_station_idx = i
                
        except simpy.Interrupt:
            logger.info(f"Train {self.train.number} simulation interrupted")
    
    def _travel_with_disruption_check(self, travel_time: float, destination: Dict):
        """Travel with periodic disruption checks"""
        elapsed = 0
        check_interval = 1.0  # Check every minute
        
        while elapsed < travel_time:
            # Check for disruptions
            disruptions = self.network.get_active_disruptions()
            affected = False
            
            for disruption in disruptions:
                if self._is_affected_by_disruption(disruption):
                    affected = True
                    # Add delay based on disruption severity
                    additional_delay = disruption.severity * 5
                    self.train.delay_minutes += additional_delay
                    self.train.status = TrainStatus.DELAYED
                    
                    self._log_event("DISRUPTION_IMPACT", f"km_{disruption.location_km}", {
                        "disruption_type": disruption.type.value,
                        "additional_delay": additional_delay
                    })
                    break
            
            # Adjust speed based on conditions
            if affected:
                self.train.current_speed = max(20, self.train.current_speed * 0.5)
            else:
                self.train.current_speed = min(self.train.max_speed, self.train.current_speed * 1.1)
            
            # Wait for next check
            next_check = min(check_interval, travel_time - elapsed)
            yield self.env.timeout(next_check)
            elapsed += next_check
            
            # Update position
            distance_covered = (self.train.current_speed / 60) * next_check
            self.train.current_km += distance_covered
    
    def _calculate_travel_time(self, from_station: Dict, to_station: Dict) -> float:
        """Calculate travel time between stations"""
        distance = abs(to_station['km'] - from_station['km'])
        base_speed = self.train.max_speed
        
        # Adjust for train type
        if self.train.type == TrainType.FREIGHT:
            base_speed *= 0.7
        elif self.train.type == TrainType.EXPRESS:
            base_speed *= 1.1
        
        # Add some randomness for realism
        speed_variation = np.random.normal(1.0, 0.1)
        actual_speed = base_speed * max(0.5, speed_variation)
        
        travel_time = (distance / actual_speed) * 60  # Convert to minutes
        return max(5, travel_time)  # Minimum 5 minutes
    
    def _calculate_stop_time(self, station: Dict) -> float:
        """Calculate stop time at station"""
        base_stop_time = {
            TrainType.EXPRESS: 2,
            TrainType.PASSENGER: 3,
            TrainType.LOCAL: 1,
            TrainType.FREIGHT: 8
        }
        
        stop_time = base_stop_time.get(self.train.type, 3)
        
        # Add randomness
        variation = np.random.normal(1.0, 0.2)
        return max(1, stop_time * variation)
    
    def _is_affected_by_disruption(self, disruption: Disruption) -> bool:
        """Check if train is affected by disruption"""
        distance_to_disruption = abs(self.train.current_km - disruption.location_km)
        return distance_to_disruption < 10  # Within 10km
    
    def _log_event(self, event_type: str, location: str, details: Dict):
        """Log simulation event"""
        event = SimulationEvent(
            timestamp=self.env.now,
            event_type=event_type,
            train_id=self.train.id,
            location=location,
            details=details
        )
        self.events.append(event)
        self.network.log_event(event)

class NetworkSimulation:
    """Main network simulation environment"""
    
    def __init__(self):
        self.env = simpy.Environment()
        self.trains: Dict[str, TrainSimulation] = {}
        self.disruptions: List[Disruption] = []
        self.events: List[SimulationEvent] = []
        self.is_running = False
        self.simulation_speed = settings.SIMULATION_SPEED
        
        # Performance tracking
        self.metrics = {
            'trains_processed': 0,
            'total_delay': 0,
            'on_time_trains': 0,
            'disruptions_handled': 0
        }
        
        logger.info("🚂 Network simulation initialized")
    
    def add_train(self, train: Train):
        """Add train to simulation"""
        train_sim = TrainSimulation(self.env, train, self)
        self.trains[train.id] = train_sim
        
        # Schedule train to start
        start_delay = len(self.trains) * 2  # Stagger train starts
        self.env.process(self._delayed_start(train_sim, start_delay))
        
        logger.info(f"Added train {train.number} to simulation")
    
    def _delayed_start(self, train_sim: TrainSimulation, delay: float):
        """Start train simulation with delay"""
        yield self.env.timeout(delay)
        yield self.env.process(train_sim.run())
    
    def inject_disruption(self, disruption: Disruption):
        """Inject disruption into simulation"""
        self.disruptions.append(disruption)
        logger.info(f"Injected disruption: {disruption.type} at km {disruption.location_km}")
        
        # Schedule disruption resolution
        self.env.process(self._resolve_disruption(disruption))
    
    def _resolve_disruption(self, disruption: Disruption):
        """Automatically resolve disruption after duration"""
        yield self.env.timeout(disruption.estimated_duration_minutes)
        
        if disruption in self.disruptions:
            disruption.is_resolved = True
            self.disruptions.remove(disruption)
            self.metrics['disruptions_handled'] += 1
            logger.info(f"Resolved disruption: {disruption.type}")
    
    def get_active_disruptions(self) -> List[Disruption]:
        """Get currently active disruptions"""
        return [d for d in self.disruptions if not d.is_resolved]
    
    def log_event(self, event: SimulationEvent):
        """Log simulation event"""
        self.events.append(event)
        
        # Update metrics
        if event.event_type == "ARRIVAL":
            self.metrics['trains_processed'] += 1
            delay = event.details.get('delay', 0)
            self.metrics['total_delay'] += delay
            
            if delay <= 5:  # On-time threshold
                self.metrics['on_time_trains'] += 1
    
    def get_current_state(self) -> SectionState:
        """Get current simulation state"""
        trains = []
        for train_sim in self.trains.values():
            train = train_sim.train
            # Update train status based on simulation
            if train.delay_minutes > 10:
                train.status = TrainStatus.DELAYED
            elif train.delay_minutes < -2:
                train.status = TrainStatus.AHEAD
            else:
                train.status = TrainStatus.ON_TIME
            
            trains.append(train)
        
        # Calculate performance metrics
        total_trains = max(1, self.metrics['trains_processed'])
        on_time_percentage = (self.metrics['on_time_trains'] / total_trains) * 100
        avg_delay = self.metrics['total_delay'] / total_trains
        
        return SectionState(
            section_id="main_section",
            trains=trains,
            disruptions=self.get_active_disruptions(),
            throughput_trains_per_hour=self._calculate_throughput(),
            on_time_percentage=on_time_percentage,
            average_delay_minutes=avg_delay,
            total_trains_today=len(self.trains)
        )
    
    def _calculate_throughput(self) -> float:
        """Calculate current throughput"""
        if self.env.now == 0:
            return 0.0
        
        hours_elapsed = self.env.now / 60.0
        return self.metrics['trains_processed'] / max(0.1, hours_elapsed)
    
    def run_simulation_step(self, duration: float = 1.0):
        """Run simulation for specified duration"""
        target_time = self.env.now + duration
        while self.env.now < target_time and self.env.peek() < target_time:
            self.env.step()
    
    def reset_simulation(self):
        """Reset simulation to initial state"""
        self.env = simpy.Environment()
        self.trains.clear()
        self.disruptions.clear()
        self.events.clear()
        self.metrics = {
            'trains_processed': 0,
            'total_delay': 0,
            'on_time_trains': 0,
            'disruptions_handled': 0
        }
        logger.info("Simulation reset")

class SimulationEngine:
    """Main simulation engine managing multiple scenarios"""
    
    def __init__(self):
        self.main_simulation = NetworkSimulation()
        self.what_if_simulations: Dict[str, NetworkSimulation] = {}
        self.is_running = False
        self.update_callbacks: List[Callable] = []
        
        # Initialize with demo trains
        self._initialize_demo_trains()
        
        logger.info("🎮 Simulation Engine initialized")
    
    def _initialize_demo_trains(self):
        """Initialize simulation with demo trains"""
        demo_trains = [
            Train(
                number="12034",
                name="Shatabdi Express",
                type=TrainType.EXPRESS,
                priority=1,
                origin="GZB",
                destination="LKO",
                scheduled_departure=datetime.now(),
                scheduled_arrival=datetime.now() + timedelta(hours=2),
                max_speed=160,
                current_km=0
            ),
            Train(
                number="64078",
                name="Local EMU",
                type=TrainType.LOCAL,
                priority=4,
                origin="GZB",
                destination="MB",
                scheduled_departure=datetime.now() + timedelta(minutes=10),
                scheduled_arrival=datetime.now() + timedelta(hours=1, minutes=30),
                max_speed=100,
                current_km=5
            ),
            Train(
                number="F4521",
                name="Freight Special",
                type=TrainType.FREIGHT,
                priority=3,
                origin="HPU",
                destination="CNB",
                scheduled_departure=datetime.now() - timedelta(minutes=30),
                scheduled_arrival=datetime.now() + timedelta(hours=3),
                max_speed=80,
                current_km=45
            )
        ]
        
        for train in demo_trains:
            self.main_simulation.add_train(train)
    
    async def run_simulation(self):
        """Run the main simulation loop"""
        self.is_running = True
        logger.info("🚀 Starting simulation engine...")
        
        while self.is_running:
            try:
                # Run simulation step
                self.main_simulation.run_simulation_step(1.0)  # 1 minute step
                
                # Notify callbacks
                current_state = self.main_simulation.get_current_state()
                for callback in self.update_callbacks:
                    try:
                        await callback(current_state)
                    except Exception as e:
                        logger.error(f"Callback error: {e}")
                
                # Sleep based on simulation speed
                await asyncio.sleep(1.0 / self.simulation_speed)
                
            except Exception as e:
                logger.error(f"Simulation error: {e}")
                await asyncio.sleep(1)
    
    async def inject_disruption(self, disruption_data: Dict[str, Any]):
        """Inject disruption for demo purposes"""
        disruption = Disruption(
            type=DisruptionType.ENGINE_FAILURE,
            description=disruption_data.get('description', 'Engine failure'),
            location_km=disruption_data.get('location_km', 45),
            track_id="main_line",
            severity=disruption_data.get('severity', 4),
            estimated_duration_minutes=disruption_data.get('duration', 30)
        )
        
        self.main_simulation.inject_disruption(disruption)
        logger.info(f"Demo disruption injected: {disruption.type}")
    
    def create_what_if_scenario(self, scenario_id: str, modifications: List[Dict]) -> str:
        """Create what-if simulation scenario"""
        # Clone main simulation
        what_if_sim = NetworkSimulation()
        
        # Copy current state
        current_state = self.main_simulation.get_current_state()
        for train in current_state.trains:
            what_if_sim.add_train(train)
        
        # Apply modifications
        for mod in modifications:
            if mod['type'] == 'delay_train':
                train_id = mod['train_id']
                delay_minutes = mod['delay_minutes']
                if train_id in what_if_sim.trains:
                    what_if_sim.trains[train_id].train.delay_minutes += delay_minutes
        
        self.what_if_simulations[scenario_id] = what_if_sim
        return scenario_id
    
    def run_what_if_scenario(self, scenario_id: str, duration_minutes: float = 60) -> Dict[str, Any]:
        """Run what-if scenario and return results"""
        if scenario_id not in self.what_if_simulations:
            return {"error": "Scenario not found"}
        
        sim = self.what_if_simulations[scenario_id]
        
        # Run simulation
        sim.run_simulation_step(duration_minutes)
        
        # Get results
        final_state = sim.get_current_state()
        
        return {
            "scenario_id": scenario_id,
            "final_state": asdict(final_state),
            "performance": {
                "total_delay": sim.metrics['total_delay'],
                "on_time_percentage": final_state.on_time_percentage,
                "throughput": final_state.throughput_trains_per_hour
            }
        }
    
    def add_update_callback(self, callback: Callable):
        """Add callback for simulation updates"""
        self.update_callbacks.append(callback)
    
    def get_current_state(self) -> SectionState:
        """Get current simulation state"""
        return self.main_simulation.get_current_state()
    
    def stop_simulation(self):
        """Stop the simulation"""
        self.is_running = False
        logger.info("🛑 Simulation engine stopped")
