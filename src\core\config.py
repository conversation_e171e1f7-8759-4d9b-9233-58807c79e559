"""
SAMAY Configuration Settings
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os

class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "SAMAY Railway Traffic Management"
    VERSION: str = "2.0.0"
    DEBUG: bool = True
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # Database
    DATABASE_URL: str = "sqlite:///./samay.db"
    REDIS_URL: str = "redis://localhost:6379"
    
    # AI Configuration
    RL_LEARNING_RATE: float = 0.0003
    RL_BATCH_SIZE: int = 64
    RL_MEMORY_SIZE: int = 100000
    
    # Optimization Parameters
    CP_SAT_TIME_LIMIT: int = 30  # seconds
    GA_POPULATION_SIZE: int = 100
    GA_GENERATIONS: int = 50
    GA_MUTATION_RATE: float = 0.1
    
    # Simulation Parameters
    SIMULATION_SPEED: float = 1.0  # Real-time multiplier
    MAX_TRAINS_PER_SECTION: int = 50
    SAFETY_HEADWAY_MINUTES: int = 3
    
    # Railway Network Configuration
    SECTION_LENGTH_KM: float = 100.0
    MAX_TRAIN_SPEED_KMH: float = 160.0
    STATION_COUNT: int = 8
    
    # Demo Configuration
    DEMO_MODE: bool = True
    AUTO_INJECT_DISRUPTIONS: bool = False
    DISRUPTION_INTERVAL_MINUTES: int = 15
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Global settings instance
settings = Settings()

# Train Types Configuration
TRAIN_TYPES = {
    "EXPRESS": {
        "priority": 1,
        "max_speed": 160,
        "color": "#FF6B6B",
        "icon": "🚄"
    },
    "PASSENGER": {
        "priority": 2,
        "max_speed": 120,
        "color": "#4ECDC4",
        "icon": "🚃"
    },
    "FREIGHT": {
        "priority": 3,
        "max_speed": 80,
        "color": "#45B7D1",
        "icon": "🚂"
    },
    "LOCAL": {
        "priority": 4,
        "max_speed": 100,
        "color": "#96CEB4",
        "icon": "🚊"
    }
}

# Station Configuration
STATIONS = [
    {"id": "GZB", "name": "Ghaziabad", "km": 0, "platforms": 6},
    {"id": "HPU", "name": "Hapur", "km": 15, "platforms": 4},
    {"id": "AMR", "name": "Amroha", "km": 35, "platforms": 3},
    {"id": "MB", "name": "Moradabad", "km": 55, "platforms": 5},
    {"id": "BE", "name": "Bareilly", "km": 75, "platforms": 4},
    {"id": "LKO", "name": "Lucknow", "km": 95, "platforms": 8},
    {"id": "CNB", "name": "Kanpur Central", "km": 120, "platforms": 10},
    {"id": "TDL", "name": "Tundla", "km": 150, "platforms": 6}
]

# Signal Configuration
SIGNAL_TYPES = {
    "HOME": {"color": "#FF0000", "authority": "STOP"},
    "DISTANT": {"color": "#FFFF00", "authority": "CAUTION"},
    "ROUTING": {"color": "#00FF00", "authority": "PROCEED"}
}

# Performance Thresholds
PERFORMANCE_THRESHOLDS = {
    "EXCELLENT": {"on_time_percentage": 95, "avg_delay_minutes": 2},
    "GOOD": {"on_time_percentage": 85, "avg_delay_minutes": 5},
    "FAIR": {"on_time_percentage": 75, "avg_delay_minutes": 10},
    "POOR": {"on_time_percentage": 60, "avg_delay_minutes": 20}
}
