"""
SAMAY Optimization Engine
Constraint Programming (CP-SAT) solver for optimal train scheduling
"""

from ortools.sat.python import cp_model
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

from ..models.railway_models import Train, TrainType
from .config import settings

logger = logging.getLogger(__name__)

class OptimizationEngine:
    """
    Constraint Programming based optimization engine for train scheduling
    Uses Google OR-Tools CP-SAT solver for optimal conflict-free scheduling
    """
    
    def __init__(self):
        self.model = None
        self.solver = cp_model.CpSolver()
        self.solver.parameters.max_time_in_seconds = settings.CP_SAT_TIME_LIMIT
        
        # Problem parameters
        self.time_horizon = 120  # minutes
        self.time_granularity = 1  # minute intervals
        self.safety_headway = settings.SAFETY_HEADWAY_MINUTES
        
        logger.info("🔧 Optimization Engine initialized with CP-SAT solver")
    
    async def solve_scheduling_problem(
        self, 
        trains: List[Train], 
        constraints: Dict[str, Any],
        time_horizon_hours: int = 2
    ) -> Dict[str, Any]:
        """
        Solve the train scheduling optimization problem
        
        Returns:
            Optimal schedule with train timings and routes
        """
        logger.info(f"🧮 Solving scheduling problem for {len(trains)} trains")
        
        self.time_horizon = time_horizon_hours * 60
        self.model = cp_model.CpModel()
        
        # Decision variables
        variables = self._create_decision_variables(trains)
        
        # Add constraints
        self._add_safety_constraints(trains, variables, constraints)
        self._add_capacity_constraints(trains, variables, constraints)
        self._add_schedule_constraints(trains, variables, constraints)
        
        # Set objective
        self._set_objective(trains, variables, constraints)
        
        # Solve
        status = self.solver.Solve(self.model)
        
        if status == cp_model.OPTIMAL or status == cp_model.FEASIBLE:
            schedule = self._extract_solution(trains, variables)
            logger.info("✅ Optimal schedule found")
            return schedule
        else:
            logger.warning("⚠️ No feasible solution found, using heuristic")
            return self._generate_heuristic_schedule(trains)
    
    def _create_decision_variables(self, trains: List[Train]) -> Dict[str, Any]:
        """Create decision variables for the optimization problem"""
        variables = {
            'arrival_times': {},
            'departure_times': {},
            'track_assignments': {},
            'platform_assignments': {},
            'route_choices': {}
        }
        
        stations = self._get_stations()
        tracks = self._get_tracks()
        
        for train in trains:
            train_id = train.id
            
            # Arrival and departure times at each station
            for station in stations:
                # Time variables (in minutes from start)
                variables['arrival_times'][(train_id, station)] = (
                    self.model.NewIntVar(0, self.time_horizon, 
                                       f'arrival_{train_id}_{station}')
                )
                variables['departure_times'][(train_id, station)] = (
                    self.model.NewIntVar(0, self.time_horizon, 
                                       f'departure_{train_id}_{station}')
                )
                
                # Platform assignment (if station has multiple platforms)
                platform_count = self._get_platform_count(station)
                if platform_count > 1:
                    variables['platform_assignments'][(train_id, station)] = (
                        self.model.NewIntVar(1, platform_count,
                                           f'platform_{train_id}_{station}')
                    )
            
            # Track assignments for each segment
            for track in tracks:
                variables['track_assignments'][(train_id, track)] = (
                    self.model.NewBoolVar(f'track_{train_id}_{track}')
                )
        
        return variables
    
    def _add_safety_constraints(self, trains: List[Train], variables: Dict, constraints: Dict):
        """Add safety headway constraints"""
        tracks = self._get_tracks()
        
        for track in tracks:
            # Get all trains using this track
            track_trains = []
            for train in trains:
                if self._train_uses_track(train, track):
                    track_trains.append(train)
            
            # Add headway constraints between consecutive trains
            for i, train1 in enumerate(track_trains):
                for j, train2 in enumerate(track_trains):
                    if i != j:
                        # Ensure minimum headway between trains
                        self._add_headway_constraint(
                            train1, train2, track, variables
                        )
    
    def _add_capacity_constraints(self, trains: List[Train], variables: Dict, constraints: Dict):
        """Add track and platform capacity constraints"""
        stations = self._get_stations()
        tracks = self._get_tracks()
        
        # Track capacity constraints (single track sections)
        for track in tracks:
            if not self._is_bidirectional(track):
                self._add_single_track_constraint(trains, track, variables)
        
        # Platform capacity constraints
        for station in stations:
            platform_count = self._get_platform_count(station)
            self._add_platform_capacity_constraint(
                trains, station, platform_count, variables
            )
    
    def _add_schedule_constraints(self, trains: List[Train], variables: Dict, constraints: Dict):
        """Add schedule adherence constraints"""
        for train in trains:
            # Departure must be after arrival
            stations = self._get_train_stations(train)
            for station in stations:
                if (train.id, station) in variables['arrival_times']:
                    arrival_var = variables['arrival_times'][(train.id, station)]
                    departure_var = variables['departure_times'][(train.id, station)]
                    
                    # Minimum stop time
                    min_stop_time = self._get_min_stop_time(train, station)
                    self.model.Add(departure_var >= arrival_var + min_stop_time)
            
            # Sequential station constraints
            for i in range(len(stations) - 1):
                current_station = stations[i]
                next_station = stations[i + 1]
                
                departure_current = variables['departure_times'][(train.id, current_station)]
                arrival_next = variables['arrival_times'][(train.id, next_station)]
                
                # Travel time between stations
                travel_time = self._get_travel_time(train, current_station, next_station)
                self.model.Add(arrival_next >= departure_current + travel_time)
    
    def _set_objective(self, trains: List[Train], variables: Dict, constraints: Dict):
        """Set the optimization objective"""
        objective_terms = []
        
        # Minimize total weighted delay
        for train in trains:
            weight = self._get_priority_weight(train)
            
            # Calculate delay from scheduled time
            scheduled_time = self._get_scheduled_departure_minutes(train)
            if scheduled_time is not None:
                stations = self._get_train_stations(train)
                if stations:
                    departure_var = variables['departure_times'][(train.id, stations[0])]
                    delay_var = self.model.NewIntVar(0, self.time_horizon, f'delay_{train.id}')
                    
                    # delay = max(0, actual_departure - scheduled_departure)
                    self.model.AddMaxEquality(delay_var, [
                        departure_var - scheduled_time, 0
                    ])
                    
                    objective_terms.append(weight * delay_var)
        
        # Maximize throughput (minimize total travel time)
        for train in trains:
            stations = self._get_train_stations(train)
            if len(stations) >= 2:
                start_departure = variables['departure_times'][(train.id, stations[0])]
                end_arrival = variables['arrival_times'][(train.id, stations[-1])]
                travel_time = end_arrival - start_departure
                objective_terms.append(travel_time)
        
        # Set objective to minimize
        if objective_terms:
            self.model.Minimize(sum(objective_terms))
    
    def _extract_solution(self, trains: List[Train], variables: Dict) -> Dict[str, Any]:
        """Extract the optimal solution from the solver"""
        schedule = {
            'generated_at': datetime.now(),
            'trains': {},
            'performance': {
                'total_delay_minutes': 0,
                'on_time_trains': 0,
                'optimization_time_seconds': self.solver.WallTime()
            }
        }
        
        total_delay = 0
        on_time_count = 0
        
        for train in trains:
            train_schedule = {
                'train_id': train.id,
                'stations': [],
                'total_delay_minutes': 0
            }
            
            stations = self._get_train_stations(train)
            for station in stations:
                if (train.id, station) in variables['arrival_times']:
                    arrival_time = self.solver.Value(
                        variables['arrival_times'][(train.id, station)]
                    )
                    departure_time = self.solver.Value(
                        variables['departure_times'][(train.id, station)]
                    )
                    
                    platform = None
                    if (train.id, station) in variables['platform_assignments']:
                        platform = self.solver.Value(
                            variables['platform_assignments'][(train.id, station)]
                        )
                    
                    station_entry = {
                        'station': station,
                        'arrival_minutes': arrival_time,
                        'departure_minutes': departure_time,
                        'platform': platform
                    }
                    train_schedule['stations'].append(station_entry)
            
            # Calculate delay
            scheduled_departure = self._get_scheduled_departure_minutes(train)
            if scheduled_departure and train_schedule['stations']:
                actual_departure = train_schedule['stations'][0]['departure_minutes']
                delay = max(0, actual_departure - scheduled_departure)
                train_schedule['total_delay_minutes'] = delay
                total_delay += delay
                
                if delay <= 5:  # Consider on-time if delay <= 5 minutes
                    on_time_count += 1
            
            schedule['trains'][train.id] = train_schedule
        
        schedule['performance']['total_delay_minutes'] = total_delay
        schedule['performance']['on_time_trains'] = on_time_count
        schedule['performance']['on_time_percentage'] = (
            on_time_count / len(trains) * 100 if trains else 100
        )
        
        return schedule
    
    def _generate_heuristic_schedule(self, trains: List[Train]) -> Dict[str, Any]:
        """Generate a heuristic schedule when optimization fails"""
        logger.info("🔄 Generating heuristic schedule...")
        
        # Simple FIFO scheduling with priority adjustments
        sorted_trains = sorted(trains, key=lambda t: (t.priority, t.scheduled_departure))
        
        schedule = {
            'generated_at': datetime.now(),
            'trains': {},
            'performance': {
                'total_delay_minutes': 0,
                'on_time_trains': len(trains),
                'optimization_time_seconds': 0,
                'is_heuristic': True
            }
        }
        
        current_time = 0
        for train in sorted_trains:
            train_schedule = {
                'train_id': train.id,
                'stations': [],
                'total_delay_minutes': 0
            }
            
            stations = self._get_train_stations(train)
            for i, station in enumerate(stations):
                if i == 0:
                    # First station - departure
                    departure_time = current_time + (train.priority * 5)
                    station_entry = {
                        'station': station,
                        'arrival_minutes': departure_time,
                        'departure_minutes': departure_time,
                        'platform': 1
                    }
                else:
                    # Subsequent stations
                    travel_time = 15  # Simplified travel time
                    arrival_time = current_time + travel_time
                    departure_time = arrival_time + 2  # 2-minute stop
                    
                    station_entry = {
                        'station': station,
                        'arrival_minutes': arrival_time,
                        'departure_minutes': departure_time,
                        'platform': 1
                    }
                    current_time = departure_time
                
                train_schedule['stations'].append(station_entry)
            
            schedule['trains'][train.id] = train_schedule
            current_time += self.safety_headway  # Add headway for next train
        
        return schedule
    
    # Helper methods
    def _get_stations(self) -> List[str]:
        """Get list of stations"""
        from .config import STATIONS
        return [station['id'] for station in STATIONS]
    
    def _get_tracks(self) -> List[str]:
        """Get list of track segments"""
        stations = self._get_stations()
        tracks = []
        for i in range(len(stations) - 1):
            tracks.append(f"{stations[i]}-{stations[i+1]}")
        return tracks
    
    def _get_platform_count(self, station: str) -> int:
        """Get number of platforms at station"""
        from .config import STATIONS
        for s in STATIONS:
            if s['id'] == station:
                return s['platforms']
        return 2  # Default
    
    def _get_priority_weight(self, train: Train) -> float:
        """Get priority weight for train"""
        weights = {
            TrainType.EXPRESS: 1.0,
            TrainType.PASSENGER: 0.8,
            TrainType.LOCAL: 0.6,
            TrainType.FREIGHT: 0.4
        }
        return weights.get(train.type, 0.5)
    
    def _get_scheduled_departure_minutes(self, train: Train) -> Optional[int]:
        """Get scheduled departure time in minutes from start"""
        if train.scheduled_departure:
            # Convert to minutes from midnight
            return train.scheduled_departure.hour * 60 + train.scheduled_departure.minute
        return None
    
    def _get_train_stations(self, train: Train) -> List[str]:
        """Get stations for train route"""
        # Simplified - return all stations for now
        return self._get_stations()[:4]  # First 4 stations
    
    def _train_uses_track(self, train: Train, track: str) -> bool:
        """Check if train uses specific track"""
        return True  # Simplified - all trains use all tracks
    
    def _is_bidirectional(self, track: str) -> bool:
        """Check if track is bidirectional"""
        return True  # Simplified - all tracks bidirectional
    
    def _get_min_stop_time(self, train: Train, station: str) -> int:
        """Get minimum stop time at station"""
        if train.type == TrainType.EXPRESS:
            return 1  # Express trains stop briefly
        elif train.type == TrainType.FREIGHT:
            return 5  # Freight trains need more time
        return 2  # Default stop time
    
    def _get_travel_time(self, train: Train, from_station: str, to_station: str) -> int:
        """Get travel time between stations"""
        # Simplified calculation
        base_time = 15  # 15 minutes base travel time
        if train.type == TrainType.FREIGHT:
            return int(base_time * 1.5)  # Freight trains are slower
        return base_time
    
    def _add_headway_constraint(self, train1: Train, train2: Train, track: str, variables: Dict):
        """Add headway constraint between two trains"""
        # This would add the actual headway constraint
        # Simplified for now
        pass
    
    def _add_single_track_constraint(self, trains: List[Train], track: str, variables: Dict):
        """Add single track capacity constraint"""
        # This would ensure only one train on single track at a time
        pass
    
    def _add_platform_capacity_constraint(self, trains: List[Train], station: str, 
                                        platform_count: int, variables: Dict):
        """Add platform capacity constraint"""
        # This would ensure platform capacity is not exceeded
        pass
