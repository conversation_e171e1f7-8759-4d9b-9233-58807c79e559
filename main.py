"""
SAMAY: Smart AI-Powered Railway Traffic Management System
Main Application Entry Point
"""

import asyncio
import uvicorn
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import json
from typing import List
import logging

from src.core.config import settings
from src.api.routes import api_router
from src.core.simulation_engine import SimulationEngine
from src.core.ai_brain import AIBrain
from src.core.websocket_manager import WebSocketManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances
simulation_engine = None
ai_brain = None
websocket_manager = WebSocketManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    global simulation_engine, ai_brain
    
    logger.info("🚂 Starting SAMAY Railway Traffic Management System...")
    
    # Initialize core components
    simulation_engine = SimulationEngine()
    ai_brain = AIBrain()
    
    # Start background tasks
    asyncio.create_task(simulation_engine.run_simulation())
    asyncio.create_task(ai_brain.start_optimization_loop())
    
    logger.info("✅ SAMAY System initialized successfully!")
    
    yield
    
    logger.info("🛑 Shutting down SAMAY System...")

# Create FastAPI application
app = FastAPI(
    title="SAMAY - Railway Traffic Management System",
    description="AI-Powered Dispatch Assistant for Indian Railways",
    version="2.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def get_dashboard():
    """Serve the main dashboard"""
    with open("static/index.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message.get("type") == "ping":
                await websocket.send_text(json.dumps({"type": "pong"}))
            elif message.get("type") == "inject_disruption":
                # Handle disruption injection for demo
                await handle_disruption_injection(message.get("data"))
                
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

async def handle_disruption_injection(disruption_data):
    """Handle disruption injection for demo purposes"""
    global simulation_engine, ai_brain
    
    if simulation_engine and ai_brain:
        # Inject disruption into simulation
        await simulation_engine.inject_disruption(disruption_data)
        
        # Trigger AI re-optimization
        recommendations = await ai_brain.handle_disruption(disruption_data)
        
        # Broadcast updates to all connected clients
        await websocket_manager.broadcast({
            "type": "disruption_injected",
            "disruption": disruption_data,
            "recommendations": recommendations
        })

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "system": "SAMAY v2.0",
        "components": {
            "simulation_engine": simulation_engine is not None,
            "ai_brain": ai_brain is not None,
            "websocket_connections": len(websocket_manager.active_connections)
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
