"""
SAMAY WebSocket Manager
Real-time communication for live updates
"""

import json
import asyncio
from typing import List, Dict, Any
from fastapi import WebSocket
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    """Manages WebSocket connections for real-time updates"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket):
        """Accept new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_info[websocket] = {
            "connected_at": asyncio.get_event_loop().time(),
            "client_ip": websocket.client.host if websocket.client else "unknown"
        }
        logger.info(f"WebSocket connected: {len(self.active_connections)} total connections")
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if websocket in self.connection_info:
            del self.connection_info[websocket]
        logger.info(f"WebSocket disconnected: {len(self.active_connections)} total connections")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast message to all connected WebSockets"""
        if not self.active_connections:
            return
        
        message_str = json.dumps(message)
        disconnected = []
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message_str)
            except Exception as e:
                logger.error(f"Error broadcasting to connection: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def broadcast_state_update(self, state_data: Dict[str, Any]):
        """Broadcast railway state update"""
        message = {
            "type": "state_update",
            "timestamp": asyncio.get_event_loop().time(),
            "data": state_data
        }
        await self.broadcast(message)
    
    async def broadcast_disruption_alert(self, disruption_data: Dict[str, Any]):
        """Broadcast disruption alert"""
        message = {
            "type": "disruption_alert",
            "timestamp": asyncio.get_event_loop().time(),
            "data": disruption_data,
            "severity": "high"
        }
        await self.broadcast(message)
    
    async def broadcast_recommendation(self, recommendation_data: Dict[str, Any]):
        """Broadcast AI recommendation"""
        message = {
            "type": "ai_recommendation",
            "timestamp": asyncio.get_event_loop().time(),
            "data": recommendation_data
        }
        await self.broadcast(message)
    
    def get_connection_count(self) -> int:
        """Get number of active connections"""
        return len(self.active_connections)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        current_time = asyncio.get_event_loop().time()
        
        stats = {
            "total_connections": len(self.active_connections),
            "connections": []
        }
        
        for websocket, info in self.connection_info.items():
            connection_duration = current_time - info["connected_at"]
            stats["connections"].append({
                "client_ip": info["client_ip"],
                "duration_seconds": round(connection_duration, 2),
                "is_active": websocket in self.active_connections
            })
        
        return stats
